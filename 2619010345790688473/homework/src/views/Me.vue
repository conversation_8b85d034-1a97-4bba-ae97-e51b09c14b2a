<template>
  <div class="me-container">
    <div class="header">
      <div class="header-text">
        <span>web前端开发大作页</span>
        <span>2023级计算机237班张富, 学号: 2303013073</span>
      </div>
    </div>
    <div class="user-info">
      <img
        src="../asserts/touxiang.png"
        alt="avatar"
        style="width: 100px; height: 100px;"
      />
      <div class="user-details">
        <p>用户名称:小陈每天都要笑</p>
        <p>用户账号:2303013073</p>
        <el-button type="text">+状态</el-button>
      </div>
      <el-icon><Setting /></el-icon>
    </div>
    <div class="settings">
      <div class="setting-item" v-for="(item, index) in 5" :key="index">
        <span>设置</span>
        <el-icon><ArrowRight /></el-icon>
      </div>
    </div>
  </div>
</template>

<script>
import { Setting, ArrowRight } from '@element-plus/icons-vue';

export default {
  components: {
    Setting,
    ArrowRight
  }
};
</script>

<style scoped>
.me-container {
  padding: 20px;
  width: 800px;
  height: 900px;
  margin: auto;
  background-color: #e6f0f5;

}

.header {
  background-color: #ff9933;
  color: white;
  padding: 10px;
}

.header-text {
  display: flex;
  flex-direction: column;
}

.user-info {
  display: flex;
  align-items: center;
  margin-top: 20px;
  margin-left: 50px;
  background-color: #e6f0ff;
  padding: 20px;
  border-radius: 10px;
}

.user-details {
  flex: 1;
  margin-left: 20px;
}

.settings {
  margin-top: 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ccc;
}
</style>
