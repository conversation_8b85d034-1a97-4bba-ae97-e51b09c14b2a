import type { VNode } from 'vue';
declare const _default: import("vue").DefineComponent<{
    current: {
        type: NumberConstructor;
        default: number;
    };
}, () => VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}> | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "update-total"[], "update-total", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    current: {
        type: NumberConstructor;
        default: number;
    };
}>> & {
    "onUpdate-total"?: ((...args: any[]) => any) | undefined;
}, {
    current: number;
}>;
export default _default;
