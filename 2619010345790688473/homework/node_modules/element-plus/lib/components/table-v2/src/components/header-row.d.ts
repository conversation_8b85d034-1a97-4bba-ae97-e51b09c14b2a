import type { CSSProperties } from 'vue';
import type { ColumnCellsType } from '../types';
import type { TableV2HeaderRowProps } from '../header-row';
declare const TableV2HeaderRow: import("vue").DefineComponent<{
    readonly class: StringConstructor;
    readonly columns: {
        readonly type: import("vue").PropType<import("../common").AnyColumn[]>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly columnsStyles: {
        readonly type: import("vue").PropType<Record<import("../types").KeyType, CSSProperties>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly headerIndex: NumberConstructor;
    readonly style: {
        readonly type: import("vue").PropType<CSSProperties>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly class: StringConstructor;
    readonly columns: {
        readonly type: import("vue").PropType<import("../common").AnyColumn[]>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly columnsStyles: {
        readonly type: import("vue").PropType<Record<import("../types").KeyType, CSSProperties>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly headerIndex: NumberConstructor;
    readonly style: {
        readonly type: import("vue").PropType<CSSProperties>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}>>, {}>;
export default TableV2HeaderRow;
export type TableV2HeaderRowCellRendererParams = {
    columns: TableV2HeaderRowProps['columns'];
    column: TableV2HeaderRowProps['columns'][number];
    columnIndex: number;
    headerIndex: number;
    style: CSSProperties;
};
export type TableV2HeaderRowRendererParams = {
    cells: ColumnCellsType;
    columns: TableV2HeaderRowProps['columns'];
    headerIndex: number;
};
